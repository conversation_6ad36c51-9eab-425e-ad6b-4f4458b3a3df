<?php

namespace App\Http\Controllers\Customer\Time;

use App\Models\Constant\ConstData;
use App\Models\Customer\Employee;
use App\Models\Customer\Time\TimeRequest\TimeRequest;
use App\Http\Requests\Customer\Time\TimeRequest\GetTimeRequestRequest;
use App\Http\Requests\Customer\Time\TimeRequest\GetDiscussedTimeRequestsForEmployeeUserRequest;
use App\Http\Requests\Customer\Time\TimeRequest\GetUnDiscussedTimeRequestsForEmployeeUserRequest;
use App\Http\Requests\Customer\Time\TimeRequest\CreateTimeRequestRequest;
use App\Http\Requests\Customer\Time\TimeRequest\DiscussRequest;
use App\Http\Resources\TimeRequest as TimeRequestResource;
use App\Http\Controllers\Controller;
use App\Http\Tools\DateTool;
use App\Services\ConnectionService;
use App\Services\Time\TimeRequestService;
use App\Services\Time\SuperSalaryPeriodService;
use App\Exceptions\SystemException;

class TimeRequestController extends Controller
{
    public function getCN() {
        $service = resolve(ConnectionService::class);
        return $service->getCNForEmployeeUser();
    }

    /**
     * Цагийн хүсэлтүүдийг жагсаалтаар авах.
     * 
     * Системд нэвтэрсэн тухайн ажилтан өөрийнхөө илгээж байсан хүсэлтүүдийг жагсаалт авах зорилготой. 
     */
    public function index(GetTimeRequestRequest $request)
    {
        $limit           = $request->input(GetTimeRequestRequest::LIMIT, 10);
        $conStatus       = $request->input(GetTimeRequestRequest::CONFIRM_STATUS);
        $cn              = $this->getCN();
        $user            = auth()->user();
        $employee        = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();
        $salaryPeriodDtl = resolve(SuperSalaryPeriodService::class)->getSuperSalaryPeriodDtlByNowDate($cn, $employee->department_id);
        if (!isset($salaryPeriodDtl))
            throw new SystemException(ConstData::TIME_EXCEPTION, 311);
        $beginDate = $salaryPeriodDtl->getBeginDate();
        $endDate   = $salaryPeriodDtl->getEndDate();

        $timeRequests = TimeRequest::on($cn)->where(TimeRequest::EMPLOYEE_ID, $employee->id);
        if (isset($beginDate))
            $timeRequests = $timeRequests->where(TimeRequest::BEGIN_DATE, '>=', $beginDate);
        if (isset($endDate))
            $timeRequests = $timeRequests->where(TimeRequest::END_DATE, '<=', $endDate);
        if (isset($conStatus))
            $timeRequests = $timeRequests->where(TimeRequest::CONFIRM_STATUS, $conStatus);
        $timeRequests = $timeRequests->paginate($limit);
        return TimeRequestResource::collection($timeRequests);
    }

    /**
     * Цагийн хүсэлт илгээх
     *
     * Бүх төрлөөр цагийн хүсэлт илгээх
     */
    public function store(CreateTimeRequestRequest $request)
    {
        $beginDate      = $request->input(CreateTimeRequestRequest::PARAMETER_BEGIN_DATE);
        $endDate        = $request->input(CreateTimeRequestRequest::PARAMETER_END_DATE);
        $description    = $request->input(CreateTimeRequestRequest::PARAMETER_DESCRIPTION);
        $attStatus      = $request->input(CreateTimeRequestRequest::PARAMETER_ATT_STATUS);

        $cn        = $this->getCN();
        $user      = auth()->user();
        $employee  = Employee::on($cn)->where(Employee::PHONE, $user->phone)->first();

        $timeRequest = new TimeRequest();
        $timeRequest->setConnection($cn);
        $timeRequest->employee_id     = $employee->id;
        $timeRequest->begin_date      = DateTool::setZeroSecond($beginDate);
        $timeRequest->end_date        = DateTool::setZeroSecond($endDate);
        $timeRequest->description     = $description;
        $timeRequest->att_status      = $attStatus;
        $timeRequest->confirm_status  = TimeRequest::CONFIRM_STATUS_SENT;
        $timeRequest->created_by      = $user->id;
        $timeRequest->save();
        return new TimeRequestResource($timeRequest);
    }

    /**
     * @authenticated
     */
    public function getDiscussedTimeRequestsForEmployeeUser(GetDiscussedTimeRequestsForEmployeeUserRequest $request) {
        $filters       = $request->input(GetDiscussedTimeRequestsForEmployeeUserRequest::FILTER);
        $attStatus     = isset($filters[GetDiscussedTimeRequestsForEmployeeUserRequest::FILTER_ATT_STATUS]) ? $filters[GetDiscussedTimeRequestsForEmployeeUserRequest::FILTER_ATT_STATUS] : null;
        $confirmStatus = isset($filters[GetDiscussedTimeRequestsForEmployeeUserRequest::FILTER_CONFIRM_STATUS]) ? $filters[GetDiscussedTimeRequestsForEmployeeUserRequest::FILTER_CONFIRM_STATUS] : null;
        $cn            = $this->getCN();
        $timeRequests  = resolve(TimeRequestService::class)->getDiscussedTimeRequests($cn, true, $attStatus, $confirmStatus);
        return TimeRequestResource::collection($timeRequests);
    }

    /**
     * @authenticated
     */
    public function getUnDiscussedTimeRequestsForEmployeeUser(GetUnDiscussedTimeRequestsForEmployeeUserRequest $request) {
        $limit         = $request->input(GetUnDiscussedTimeRequestsForEmployeeUserRequest::LIMIT, 10);
        $page          = $request->input(GetUnDiscussedTimeRequestsForEmployeeUserRequest::PAGE, 1);
        $filters       = $request->input(GetUnDiscussedTimeRequestsForEmployeeUserRequest::FILTER);
        $attStatus     = isset($filters[GetUnDiscussedTimeRequestsForEmployeeUserRequest::FILTER_ATT_STATUS]) ? $filters[GetUnDiscussedTimeRequestsForEmployeeUserRequest::FILTER_ATT_STATUS] : null;
        $cn            = $this->getCN();
        $timeRequests  = resolve(TimeRequestService::class)->getUnDiscussedTimeRequests($cn, $attStatus, $limit, $page);
        return TimeRequestResource::collection($timeRequests);
    }

    /**
     * @authenticated
     */
    public function discuss($timeRequestId, DiscussRequest $request) {
        $timeRequest   = TimeRequest::on($this->getCN())->find($timeRequestId);
        $confirmStatus = $request->input(DiscussRequest::PARAMETER_CONFIRM_STATUS);
        $description   = $request->input(DiscussRequest::PARAMETER_DESCRIPTION);

        $timeRequest->confirm_status = $confirmStatus;
        $timeRequest->description    = $description;
        $timeRequest->updated_by     = auth()->user()->id;
        $timeRequest->save();
        return new TimeRequestResource($timeRequest);
    }
}
