<?php

namespace App\Http\Requests\Customer\Time\TimeRequest;

use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class TimeRequestRequest extends FormRequest
{
    const PARAMETER_BEGIN_DATE                 = 'begin_date';
    const PARAMETER_END_DATE                   = 'end_date';
    const PARAMETER_DESCRIPTION                = 'description';
    const PARAMETER_ATT_STATUS                 = 'att_status';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'begin_date'         => 'required|date',
            'end_date'           => 'required|date|after_or_equal:begin_date',
            'description'        => 'nullable|string|max:255',
            /**
             * 3: Work, 8: <PERSON><PERSON>ough, 13: Sick
             */
            'att_status'         => ['required', Rule::in(EmployeeAttendanceDtl::REQUEST_ATTENDANCE_STATUS)],
        ];
    }
}
