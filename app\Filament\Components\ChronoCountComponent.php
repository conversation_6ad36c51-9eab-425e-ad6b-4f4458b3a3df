<?php

namespace App\Filament\Components;

use Filament\Forms;

class ChronoCountComponent
{
    public static function make(string $name = 'chrono_count'): Forms\Components\Group
    {
        return Forms\Components\Group::make([
            Forms\Components\Select::make("{$name}_static_type")
                ->label('Static Chrono Type')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Select::make("{$name}_chrono_type")
                ->label('Chrono Type')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_length")
                ->label('Chrono Length')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_count")
                ->label('Count')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Hidden::make("{$name}_formula")
        ])->columns(4);
    }

    private static function updateFormula(string $name, callable $set, callable $get): void
    {
        $staticType = $get("{$name}_static_type");
        $chronoType = $get("{$name}_chrono_type");
        $length = $get("{$name}_length");
        $count = $get("{$name}_count");

        if ($staticType && $chronoType && $length !== null && $length !== '' && $count !== null && $count !== '') {
            $formula = "{{$staticType}},{{$chronoType}}*{$length},{$count}";
            $set("{$name}_formula", $formula);
        }
    }

    /**
     * Parse a formula string back into component values
     * Formula format: "{staticType},{chronoType}*length,count"
     */
    public static function parseFormula(string $formula): array
    {
        $result = [
            'static_type' => null,
            'chrono_type' => null,
            'length' => null,
            'count' => null,
        ];

        // Match pattern: {staticType},{chronoType}*length,count
        if (preg_match('/^\{(\w+)\},\{(\w+)\}\*(-?\d+),(-?\d+)$/', $formula, $matches)) {
            $staticType = $matches[1];
            $chronoType = $matches[2];
            $length = (int)$matches[3];
            $count = (int)$matches[4];

            $result['static_type'] = $staticType;
            $result['chrono_type'] = $chronoType;
            $result['length'] = $length;
            $result['count'] = $count;
        }

        return $result;
    }
}