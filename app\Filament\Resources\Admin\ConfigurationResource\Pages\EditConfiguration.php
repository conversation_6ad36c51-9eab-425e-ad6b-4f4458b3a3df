<?php

namespace App\Filament\Resources\Admin\ConfigurationResource\Pages;

use App\Filament\Resources\Admin\ConfigurationResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Database\UniqueConstraintViolationException;

class EditConfiguration extends EditRecord
{
    protected static string $resource = ConfigurationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function handleRecordUpdate($record, array $data): \Illuminate\Database\Eloquent\Model
    {
        try {
            return parent::handleRecordUpdate($record, $data);
        } catch (UniqueConstraintViolationException $e) {
            Notification::make()
                ->title('Алдаа')
                ->body('Тохиргооны ID давхардаж байна. Өөр утга оруулна уу.')
                ->danger()
                ->send();
            
            $this->halt();
        }
    }
}