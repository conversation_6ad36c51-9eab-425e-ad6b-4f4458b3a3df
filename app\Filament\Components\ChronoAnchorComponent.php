<?php

namespace App\Filament\Components;

use Filament\Forms;

class ChronoAnchorComponent
{
    public static function make(string $name = 'chrono_anchor'): Forms\Components\Group
    {
        return Forms\Components\Group::make([
            Forms\Components\Select::make("{$name}_type")
                ->label('Chrono Type')
                ->options([
                    'minute' => 'Minute',
                    'hour' => 'Hour',
                    'halfday' => 'Half Day',
                    'day' => 'Day',
                    'month' => 'Month',
                    'quarter' => 'Quarter',
                    'halfyear' => 'Half Year',
                    'year' => 'Year'
                ])
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\TextInput::make("{$name}_shift_count")
                ->label('Anchor Shift Count')
                ->numeric()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\DatePicker::make("{$name}_date")
                ->label('Anchor Date')
                ->required()
                ->reactive()
                ->afterStateUpdated(fn ($state, callable $set, callable $get) => 
                    self::updateFormula($name, $set, $get)
                ),
            Forms\Components\Hidden::make("{$name}_formula")
        ])->columns(3);
    }

    private static function updateFormula(string $name, callable $set, callable $get): void
    {
        $date = $get("{$name}_date");
        $type = $get("{$name}_type");
        $shift = $get("{$name}_shift_count");
        
        if ($date && $type && $shift !== null && $shift !== '') {
            $formula = "{$date}+{$type}*{$shift}";
            $set("{$name}_formula", $formula);
        }
    }
}