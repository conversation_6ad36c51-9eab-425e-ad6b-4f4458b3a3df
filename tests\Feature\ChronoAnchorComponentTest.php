<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Filament\Components\ChronoAnchorComponent;
use Filament\Forms\Components\Group;

class ChronoAnchorComponentTest extends TestCase
{
    /** @test */
    public function it_creates_chrono_anchor_component_with_default_name()
    {
        $component = ChronoAnchorComponent::make();
        
        $this->assertInstanceOf(Group::class, $component);
    }
    
    /** @test */
    public function it_creates_chrono_anchor_component_with_custom_name()
    {
        $component = ChronoAnchorComponent::make('test_anchor');
        
        $this->assertInstanceOf(Group::class, $component);
    }
    
    /** @test */
    public function it_has_correct_component_structure()
    {
        $component = ChronoAnchorComponent::make();

        // The component should be a Group component with child components
        $this->assertInstanceOf(Group::class, $component);
        $this->assertNotEmpty($component->getChildComponents());
    }
    
    /** @test */
    public function it_contains_required_form_fields()
    {
        $component = ChronoAnchorComponent::make('test');
        $childComponents = $component->getChildComponents();

        // Should have 5 child components: type, shift_count, date_source, custom_date, formula
        $this->assertCount(5, $childComponents);

        // Check that we have the expected field names
        $fieldNames = [];
        foreach ($childComponents as $child) {
            $fieldNames[] = $child->getName();
        }

        $this->assertContains('test_type', $fieldNames);
        $this->assertContains('test_shift_count', $fieldNames);
        $this->assertContains('test_date_source', $fieldNames);
        $this->assertContains('test_custom_date', $fieldNames);
        $this->assertContains('test_formula', $fieldNames);
    }

    /** @test */
    public function it_parses_custom_date_formula_correctly()
    {
        $formula = '2024-01-15+day*5';
        $parsed = ChronoAnchorComponent::parseFormula($formula);

        $this->assertEquals('day', $parsed['type']);
        $this->assertEquals(5, $parsed['shift_count']);
        $this->assertEquals('custom', $parsed['date_source']);
        $this->assertEquals('2024-01-15', $parsed['custom_date']);
    }

    /** @test */
    public function it_parses_model_field_formula_correctly()
    {
        $formula = 'employee.created_at+month*-2';
        $parsed = ChronoAnchorComponent::parseFormula($formula);

        $this->assertEquals('month', $parsed['type']);
        $this->assertEquals(-2, $parsed['shift_count']);
        $this->assertEquals('employee.created_at', $parsed['date_source']);
        $this->assertNull($parsed['custom_date']);
    }

    /** @test */
    public function it_parses_complex_model_field_formula_correctly()
    {
        $formula = 'employee_dtl.company_work_date+year*1';
        $parsed = ChronoAnchorComponent::parseFormula($formula);

        $this->assertEquals('year', $parsed['type']);
        $this->assertEquals(1, $parsed['shift_count']);
        $this->assertEquals('employee_dtl.company_work_date', $parsed['date_source']);
        $this->assertNull($parsed['custom_date']);
    }

    /** @test */
    public function it_handles_invalid_formula_gracefully()
    {
        $formula = 'invalid-formula';
        $parsed = ChronoAnchorComponent::parseFormula($formula);

        $this->assertNull($parsed['type']);
        $this->assertNull($parsed['shift_count']);
        $this->assertEquals('custom', $parsed['date_source']);
        $this->assertNull($parsed['custom_date']);
    }
}
