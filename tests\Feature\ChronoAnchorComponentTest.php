<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Filament\Components\ChronoAnchorComponent;
use Filament\Forms\Components\Group;

class ChronoAnchorComponentTest extends TestCase
{
    /** @test */
    public function it_creates_chrono_anchor_component_with_default_name()
    {
        $component = ChronoAnchorComponent::make();
        
        $this->assertInstanceOf(Group::class, $component);
    }
    
    /** @test */
    public function it_creates_chrono_anchor_component_with_custom_name()
    {
        $component = ChronoAnchorComponent::make('test_anchor');
        
        $this->assertInstanceOf(Group::class, $component);
    }
    
    /** @test */
    public function it_has_correct_component_structure()
    {
        $component = ChronoAnchorComponent::make();

        // The component should be a Group component with child components
        $this->assertInstanceOf(Group::class, $component);
        $this->assertNotEmpty($component->getChildComponents());
    }
    
    /** @test */
    public function it_contains_required_form_fields()
    {
        $component = ChronoAnchorComponent::make('test');
        $childComponents = $component->getChildComponents();
        
        // Should have 5 child components: type, shift_count, date_source, custom_date, formula
        $this->assertCount(5, $childComponents);
        
        // Check that we have the expected field names
        $fieldNames = [];
        foreach ($childComponents as $child) {
            $fieldNames[] = $child->getName();
        }
        
        $this->assertContains('test_type', $fieldNames);
        $this->assertContains('test_shift_count', $fieldNames);
        $this->assertContains('test_date_source', $fieldNames);
        $this->assertContains('test_custom_date', $fieldNames);
        $this->assertContains('test_formula', $fieldNames);
    }
}
