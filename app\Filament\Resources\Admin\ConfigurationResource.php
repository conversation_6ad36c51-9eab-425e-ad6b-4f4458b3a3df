<?php

namespace App\Filament\Resources\Admin;

use App\Models\Class\Can;
use App\Models\Customer\Configuration;
use App\Models\Customer\Time\Attendance\EmployeeAttendanceDtl;
use App\Filament\Resources\Admin\ConfigurationResource\Pages;
use App\Filament\Resources\Admin\ConfigurationResource\RelationManagers;
use App\Filament\Components\ChronoAnchorComponent;
use App\Filament\Components\ChronoRangeComponent;
use App\Filament\Components\ChronoCountComponent;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Tables\Table;


class ConfigurationResource extends Can
{
    protected static ?string $model = Configuration::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Тохиргоо';
    protected static ?string $modelLabel = 'тохиргоо';
    protected static ?string $navigationGroup = 'Бусад';
    protected static ?int $navigationSort = 99999;
    protected static ?string $slug = 'configurations';


    public static function form(Form $form): Form
    {
        $employeeAttendanceDtl = new EmployeeAttendanceDtl();
        $attendanceStatusOptions = $employeeAttendanceDtl->getConfigAttStatusNames();

        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make(Configuration::CONFIG_ID)
                            ->label('Тохиргооны ID')
                            ->options($attendanceStatusOptions)
                            ->required()
                            ->searchable()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                $set(Configuration::VALUE, '');
                                $set(Configuration::VALUE1, '');
                            }),
                        
                        Forms\Components\Section::make('Утга (Value)')
                            ->schema([
                                Forms\Components\Group::make([
                                    ChronoAnchorComponent::make('vacation_anchor'),
                                    Forms\Components\Hidden::make(Configuration::VALUE)
                                        ->dehydrated(true)
                                        ->dehydrateStateUsing(fn (callable $get) => $get('vacation_anchor_formula'))
                                        ->afterStateHydrated(function ($component, $state, $record) {
                                            if ($state && $record) {
                                                $parsed = ChronoAnchorComponent::parseFormula($state);
                                                $component->getContainer()->getComponent('vacation_anchor_type')->state($parsed['type']);
                                                $component->getContainer()->getComponent('vacation_anchor_shift_count')->state($parsed['shift_count']);
                                                $component->getContainer()->getComponent('vacation_anchor_date_source')->state($parsed['date_source']);
                                                $component->getContainer()->getComponent('vacation_anchor_custom_date')->state($parsed['custom_date']);
                                            }
                                        })
                                        ->reactive(),
                                ])
                                    ->visible(fn (callable $get) => $get(Configuration::CONFIG_ID) === 'ATTENDANCE_STATUS_VACATION'),
                                
                                Forms\Components\Group::make([
                                    ChronoRangeComponent::make('furlough_range'),
                                    Forms\Components\Hidden::make(Configuration::VALUE)
                                        ->dehydrated(true)
                                        ->dehydrateStateUsing(fn (callable $get) => $get('furlough_range_formula'))
                                        ->afterStateHydrated(function ($component, $state, $record) {
                                            if ($state && $record) {
                                                $parsed = ChronoRangeComponent::parseFormula($state);
                                                $component->getContainer()->getComponent('furlough_range_type')->state($parsed['type']);
                                                $component->getContainer()->getComponent('furlough_range_from')->state($parsed['from']);
                                                $component->getContainer()->getComponent('furlough_range_to')->state($parsed['to']);
                                            }
                                        })
                                        ->reactive(),
                                ])
                                    ->visible(fn (callable $get) => str_starts_with($get(Configuration::CONFIG_ID), 'ATTENDANCE_STATUS_FURLOUGH.')),
                                
                                Forms\Components\Group::make([
                                    ChronoCountComponent::make('salary_furlough_count'),
                                    Forms\Components\Hidden::make(Configuration::VALUE)
                                        ->dehydrated(true)
                                        ->dehydrateStateUsing(fn (callable $get) => $get('salary_furlough_count_formula'))
                                        ->afterStateHydrated(function ($component, $state, $record) {
                                            if ($state && $record) {
                                                $parsed = ChronoCountComponent::parseFormula($state);
                                                $component->getContainer()->getComponent('salary_furlough_count_static_type')->state($parsed['static_type']);
                                                $component->getContainer()->getComponent('salary_furlough_count_chrono_type')->state($parsed['chrono_type']);
                                                $component->getContainer()->getComponent('salary_furlough_count_length')->state($parsed['length']);
                                                $component->getContainer()->getComponent('salary_furlough_count_count')->state($parsed['count']);
                                            }
                                        })
                                        ->reactive(),
                                ])
                                    ->visible(fn (callable $get) => str_starts_with($get(Configuration::CONFIG_ID), 'ATTENDANCE_STATUS_SALARY_FURLOUGH.')),
                                
                                Forms\Components\Placeholder::make('value_disabled')
                                    ->label('Компонент идэвхгүй')
                                    ->content('Энэ тохиргооны төрөлд VALUE компонент ашиглагдахгүй')
                                    ->visible(fn (callable $get) => !in_array($get(Configuration::CONFIG_ID), ['ATTENDANCE_STATUS_VACATION']) 
                                        && !str_starts_with($get(Configuration::CONFIG_ID), 'ATTENDANCE_STATUS_FURLOUGH.')
                                        && !str_starts_with($get(Configuration::CONFIG_ID), 'ATTENDANCE_STATUS_SALARY_FURLOUGH.')
                                        && $get(Configuration::CONFIG_ID) !== null),
                            ])
                            ->visible(fn (callable $get) => $get(Configuration::CONFIG_ID) !== null),
                        
                        Forms\Components\Section::make('Утга 1 (Value1) - Анкер компонент')
                            ->schema([
                                Forms\Components\Group::make([
                                    ChronoAnchorComponent::make('anchor_config'),
                                    Forms\Components\Hidden::make(Configuration::VALUE1)
                                        ->dehydrated(true)
                                        ->dehydrateStateUsing(fn (callable $get) => $get('anchor_config_formula'))
                                        ->afterStateHydrated(function ($component, $state, $record) {
                                            if ($state && $record) {
                                                $parsed = ChronoAnchorComponent::parseFormula($state);
                                                $component->getContainer()->getComponent('anchor_config_type')->state($parsed['type']);
                                                $component->getContainer()->getComponent('anchor_config_shift_count')->state($parsed['shift_count']);
                                                $component->getContainer()->getComponent('anchor_config_date_source')->state($parsed['date_source']);
                                                $component->getContainer()->getComponent('anchor_config_custom_date')->state($parsed['custom_date']);
                                            }
                                        })
                                        ->reactive(),
                                ])
                                    ->visible(fn (callable $get) => !in_array($get(Configuration::CONFIG_ID), [
                                        'ATTENDANCE_STATUS_SICK',
                                        'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_SHORT',
                                        'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_MEDIUM',
                                    ])),
                                
                                Forms\Components\Placeholder::make('value1_disabled')
                                    ->label('Компонент идэвхгүй')
                                    ->content('Энэ тохиргооны төрөлд VALUE1 анкер компонент ашиглагдахгүй')
                                    ->visible(fn (callable $get) => in_array($get(Configuration::CONFIG_ID), [
                                        'ATTENDANCE_STATUS_SICK',
                                        'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_SHORT',
                                        'ATTENDANCE_STATUS_FURLOUGH.ATTENDANCE_STATUS_FURLOUGH_MEDIUM',
                                    ])),
                            ])
                            ->visible(fn (callable $get) => $get(Configuration::CONFIG_ID) !== null),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Configuration::CONFIG_ID)
                    ->label('Тохиргооны ID')
                    ->formatStateUsing(function (string $state): string {
                        $employeeAttendanceDtl = new EmployeeAttendanceDtl();
                        $attendanceStatusOptions = $employeeAttendanceDtl->getConfigAttStatusNames();
                        return $attendanceStatusOptions[$state] ?? $state;
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make(Configuration::VALUE)
                    ->label('Утга')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) > 50) {
                            return $state;
                        }
                        return null;
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make(Configuration::VALUE1)
                    ->label('Утга 1')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) > 50) {
                            return $state;
                        }
                        return null;
                    })
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('updated_at')->label('Шинэчлэгдсэн огноо')->dateTime()->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListConfigurations::route('/'),
            'create' => Pages\CreateConfiguration::route('/create'),
            'edit' => Pages\EditConfiguration::route('/{record}/edit'),
        ];
    }
}